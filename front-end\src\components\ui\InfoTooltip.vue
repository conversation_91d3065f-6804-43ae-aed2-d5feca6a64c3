<template>
  <span class="relative group inline-block cursor-pointer">
    <!-- Ícone -->
    <span class="text-blue-500 hover:text-blue-600 transition-colors">ℹ️</span>

    <!-- Tooltip to the LEFT -->
    <div
      class="absolute z-50 right-full top-1/2 -translate-y-1/2 mr-3 px-4 py-3 text-sm rounded-lg shadow-xl
             opacity-0 invisible group-hover:opacity-100 group-hover:visible
             pointer-events-none group-hover:pointer-events-auto
             transition-all duration-300 ease-in-out
             transform -translate-x-2 group-hover:translate-x-0
             bg-black border border-green-500 text-green-500 text-left w-80 min-w-80 max-w-96
             whitespace-normal break-words leading-relaxed font-normal"
    >
      <div class="text-green-500 text-left leading-relaxed whitespace-normal break-words">
        {{ message }}
      </div>
      <!-- Arrow pointing right (toward the icon) -->
      <div class="absolute left-full top-1/2 -translate-y-1/2 w-0 h-0 border-t-8 border-b-8 border-l-8 border-t-transparent border-b-transparent border-l-green-500"></div>
    </div>
  </span>
</template>

<script setup>
defineProps({
  message: String,
});
</script>


