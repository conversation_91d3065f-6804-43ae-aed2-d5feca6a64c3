<template>
  <span class="relative group inline-block cursor-pointer">
    <!-- Ícone -->
    <span class="text-blue-500 hover:text-blue-600 transition-colors">ℹ️</span>

    <!-- Tooltip flutuante -->
    <div
      class="absolute z-50 left-full top-1/2 -translate-y-1/2 ml-3 w-80 max-w-sm px-4 py-3 text-sm text-white bg-gray-900/95 backdrop-blur-sm border border-gray-600 rounded-lg shadow-xl
             opacity-0 invisible group-hover:opacity-100 group-hover:visible
             pointer-events-none group-hover:pointer-events-auto
             transition-all duration-300 ease-in-out
             transform translate-x-2 group-hover:translate-x-0
             sm:w-96 sm:max-w-md"
    >
      <div class="leading-relaxed text-gray-100 whitespace-normal break-words">
        {{ message }}
      </div>
      <!-- Seta -->
      <div
        class="absolute right-full top-1/2 -translate-y-1/2 w-0 h-0 border-t-[8px] border-b-[8px] border-r-[8px] border-t-transparent border-b-transparent border-r-gray-900/95"
      ></div>
    </div>

    <!-- Fallback tooltip for mobile/small screens - appears below -->
    <div
      class="absolute z-50 top-full left-1/2 -translate-x-1/2 mt-2 w-72 px-4 py-3 text-sm text-white bg-gray-900/95 backdrop-blur-sm border border-gray-600 rounded-lg shadow-xl
             opacity-0 invisible group-hover:opacity-100 group-hover:visible
             pointer-events-none group-hover:pointer-events-auto
             transition-all duration-300 ease-in-out
             transform translate-y-2 group-hover:translate-y-0
             sm:hidden"
    >
      <div class="leading-relaxed text-gray-100 whitespace-normal break-words">
        {{ message }}
      </div>
      <!-- Seta para cima -->
      <div
        class="absolute bottom-full left-1/2 -translate-x-1/2 w-0 h-0 border-l-[8px] border-r-[8px] border-b-[8px] border-l-transparent border-r-transparent border-b-gray-900/95"
      ></div>
    </div>
  </span>
</template>

<script setup>
defineProps({
  message: String,
});
</script>
