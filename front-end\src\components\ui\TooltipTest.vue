<template>
  <div class="p-8 space-y-6 bg-gray-100 dark:bg-gray-800 min-h-screen">
    <h1 class="text-2xl font-bold text-gray-900 dark:text-white mb-8">InfoTooltip Test</h1>
    
    <!-- Test different positions -->
    <div class="space-y-4">
      <h2 class="text-lg font-semibold text-gray-800 dark:text-gray-200">Position Tests</h2>
      
      <!-- Left side test -->
      <div class="flex items-center space-x-4 p-4 bg-white dark:bg-gray-700 rounded-lg">
        <InfoTooltip message="This tooltip should appear to the right of the icon and have a solid background." />
        <span class="text-gray-700 dark:text-gray-300">Left side tooltip test</span>
      </div>
      
      <!-- Center test -->
      <div class="flex items-center justify-center space-x-4 p-4 bg-white dark:bg-gray-700 rounded-lg">
        <span class="text-gray-700 dark:text-gray-300">Center tooltip test</span>
        <InfoTooltip message="This is a longer message to test how the tooltip handles multiple lines of text. It should wrap properly and maintain readability." />
        <span class="text-gray-700 dark:text-gray-300">More content</span>
      </div>
      
      <!-- Right side test -->
      <div class="flex items-center justify-end space-x-4 p-4 bg-white dark:bg-gray-700 rounded-lg">
        <span class="text-gray-700 dark:text-gray-300">Right side tooltip test</span>
        <InfoTooltip message="This tooltip is near the right edge and should still be visible." />
      </div>
    </div>
    
    <!-- Technical Analysis Simulation -->
    <div class="space-y-4">
      <h2 class="text-lg font-semibold text-gray-800 dark:text-gray-200">Technical Analysis Simulation</h2>
      
      <div class="p-4 bg-white dark:bg-gray-700 rounded-lg space-y-2">
        <p class="flex items-center space-x-2">
          <InfoTooltip message="The average price of the asset over a specific period of time."/>
          <span class="text-gray-700 dark:text-gray-300">Simple Moving Average: 42,250.00</span>
        </p>
        
        <p class="flex items-center space-x-2">
          <InfoTooltip message="The average price of the asset over a specific period of time, but with more weight given to recent prices."/>
          <span class="text-gray-700 dark:text-gray-300">Exponential Moving Average: 42,300.00</span>
        </p>
        
        <p class="flex items-center space-x-2">
          <InfoTooltip message="A momentum oscillator that measures the speed and change of price movements. Values above 70 indicate overbought conditions, while values below 30 indicate oversold conditions."/>
          <span class="text-gray-700 dark:text-gray-300">Relative Strength Index: 65.50</span>
        </p>
        
        <p class="flex items-center space-x-2">
          <InfoTooltip message="A trend-following momentum indicator that shows the relationship between two moving averages of prices. When MACD crosses above the signal line, it's often considered a bullish signal."/>
          <span class="text-gray-700 dark:text-gray-300">Moving Average Convergence Divergence: 150.20</span>
        </p>
      </div>
    </div>
    
    <!-- Mobile test -->
    <div class="sm:hidden">
      <h2 class="text-lg font-semibold text-gray-800 dark:text-gray-200">Mobile Test</h2>
      <div class="p-4 bg-white dark:bg-gray-700 rounded-lg">
        <p class="flex items-center space-x-2">
          <InfoTooltip message="On mobile, this tooltip should appear below the icon instead of to the side."/>
          <span class="text-gray-700 dark:text-gray-300">Mobile tooltip test</span>
        </p>
      </div>
    </div>
  </div>
</template>

<script setup>
import InfoTooltip from './InfoTooltip.vue';
</script>
